# ESTUN Screw Planning: Intelligent Screw Trajectory Optimization Based on Volumetric Bone Density Analysis and Geometric Constraint Integration

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/) [![SimpleITK](https://img.shields.io/badge/SimpleITK-2.0+-green.svg)](https://simpleitk.org/) [![NumPy](https://img.shields.io/badge/NumPy-1.20+-red.svg)](https://numpy.org/) [![License](https://img.shields.io/badge/ESTUN-Algo-yellow.svg)](https://opensource.org/licenses/MIT)

## ESTUN Screw Planning: 基于体积骨密度分析与几何约束融合的螺钉轨迹优化系统

![gi2LoUOGTQS2UPt_xmw8Vw](./resource/imgs/gi2LoUOGTQS2UPt_xmw8Vw.png)

## 📰 项目概述

本项目是一个基于骨密度评估和锥形空间路径积分的肩盂假体基座螺钉植入路径自动规划系统，专门用于反向肩关节置换术（Reverse Shoulder Arthroplasty, RSA）的术前规划。系统严格遵循Li等人2022年发表的学术论文算法设计，实现了从理论到实践的完整转化。

### 🎯 核心特性

- **自动化规划**：基于CT图像自动生成最优螺钉植入路径
- **骨密度评估**：使用验证的公式法进行精确骨密度计算
- **锥形空间算法**：实现论文中的核心数学模型
- **体积约束优化**：确保螺钉完全位于骨质内部
- **多模式支持**：支持传统规划和业务规划两种模式
- **高性能计算**：优化的算法实现，典型规划任务1秒内完成

## 🏥 项目背景

### 医学背景

反向肩关节置换术（RSA）是治疗严重肩关节疾病的有效手术方法，特别适用于：
- 大面积不可修复的肩袖撕裂
- 粉碎性肱骨近端骨折
- 肩关节关节炎伴骨质缺损
- 肩关节假体翻修手术

### 临床意义

传统的RSA术前规划主要依赖经验丰富的外科医生手工完成，存在以下问题：
- **主观性强**：规划结果高度依赖医生个人经验
- **耗时较长**：手工规划过程复杂，耗时较多
- **一致性差**：不同医生的规划结果可能存在显著差异
- **风险评估困难**：难以量化评估螺钉植入的安全性

### 技术挑战

肩盂基座螺钉规划面临的主要技术挑战包括：
1. **复杂的解剖结构**：肩胛骨形状不规则，骨质密度分布不均
2. **多约束优化**：需要同时满足角度约束、长度约束和安全性约束
3. **骨密度评估**：准确评估螺钉路径上的骨质强度
4. **空间几何计算**：复杂的3D几何变换和路径计算

## 🔬 技术方案

### 核心算法思路

本系统基于Li等人论文提出的算法框架，核心思路包括：

1. **锥形空间生成**：以螺钉起始点为顶点，根据约束角度生成锥形搜索空间
2. **候选路径采样**：在锥形空间内均匀采样生成候选螺钉路径
3. **骨密度积分**：沿每条候选路径计算骨密度积分值
4. **体积约束检查**：确保螺钉完全位于有效骨质内部
5. **多目标优化**：综合考虑骨密度、角度约束和安全性选择最优路径

### 主要技术栈

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **Python** | 3.8+ | 主要开发语言 |
| **SimpleITK** | 2.0+ | 医学图像处理和坐标变换 |
| **NumPy** | 1.20+ | 数值计算和数组操作 |
| **SciPy** | 1.7+ | 科学计算和优化算法 |
| **VTK** | 9.0+ | 3D可视化和几何处理 |

### 解决方案架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据输入层    │    │   算法处理层    │    │   结果输出层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • CT图像数据    │───▶│ • 骨密度计算    │───▶│ • 最优路径      │
│ • 肩胛骨掩膜    │    │ • 锥形空间生成  │    │ • 评估报告      │
│ • 参考点坐标    │    │ • 路径规划算法  │    │ • 可视化结果    │
│ • 螺钉规格      │    │ • 体积约束检查  │    │ • 统计信息      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ 实现细节

### 关键算法实现

#### 1. 骨密度计算模块 (`core/bone_density.py`)

**核心功能**：
- HU值到骨密度转换：`QCT = 17.8 + 0.7 × HU`
- 路径骨密度积分计算
- 体积约束检查（两阶段筛选）
- 批量路径评估优化

````python
def hu_to_bone_density(self, hu_value: float) -> float:
    """
    将HU值转换为骨密度值（公式法）

    Args:
        hu_value: HU值

    Returns:
        骨密度值 (mg/cc)
    """
    return self.formula_intercept + self.formula_slope * hu_value
````
#### 2. 锥形空间生成模块 (`core/cone_space.py`)

**核心功能**：
- 基于约束角度的锥形空间生成
- 候选路径均匀采样
- 螺钉对路径生成（避免干涉）
- 旋转矩阵计算（Rodrigues公式）

````python
def generate_cone_space(self, apex: Point3D, axis_direction: Vector3D) -> ConeSpace:
    """
    生成锥形空间

    Args:
        apex: 锥顶点（螺钉起始点）
        axis_direction: 锥轴方向

    Returns:
        锥形空间对象
    """
    # 计算锥底半径
    base_radius = self.screw_length * math.tan(self.constraint_angle)

    cone_space = ConeSpace(
        apex=apex,
        axis=axis_direction,
        height=self.screw_length,
        base_radius=base_radius,
        constraint_angle=math.degrees(self.constraint_angle)
    )

    return cone_space
````
#### 3. 路径规划主算法 (`core/path_planning.py`)

**核心功能**：
- 完整的规划工作流程
- 参考点验证
- 多线程规划支持
- 统计信息计算

#### 4. 业务规划模块 (`core/business_planning.py`)

**核心功能**：
- 支持3个螺钉起始坐标输入
- 自动方向判断
- 独立螺钉路径规划
- 螺钉末端坐标输出

### 数据处理流程

```mermaid
graph TD
    A[CT图像输入] --> B[图像预处理]
    B --> C[参考点选择]
    C --> D[假体参数计算]
    D --> E[锥形空间生成]
    E --> F[候选路径采样]
    F --> G[骨密度评估]
    G --> H[体积约束检查]
    H --> I[路径优化选择]
    I --> J[结果输出]
```

### 核心功能模块

| 模块名称 | 文件路径 | 主要功能 |
|---------|---------|---------|
| **几何计算** | `utils/geometry.py` | Point3D、Vector3D、ScrewPath等基础几何类 |
| **图像处理** | `utils/image_processing.py` | CT图像读取、预处理和坐标变换 |
| **坐标变换** | `utils/coordinate_transform.py` | 世界坐标与图像坐标转换 |
| **路径可视化** | `utils/path_visualizer.py` | 3D路径可视化和结果展示 |
| **文件IO** | `utils/io_utils.py` | 数据文件读写和格式转换 |
| **命令行接口** | `cli/business_cli.py` | 业务规划命令行工具 |

## 📁 项目结构

```
ScrewPlanning/
├── src/                           # 源代码目录
│   ├── core/                      # 核心算法模块
│   │   ├── __init__.py
│   │   ├── bone_density.py        # 骨密度计算和路径评估
│   │   ├── cone_space.py          # 锥形空间生成和候选路径采样
│   │   ├── path_planning.py       # 主路径规划算法
│   │   ├── business_planning.py   # 业务规划模块
│   │   └── optimization.py        # 路径优化算法
│   ├── utils/                     # 工具函数模块
│   │   ├── __init__.py
│   │   ├── geometry.py            # 3D几何计算类和函数
│   │   ├── image_processing.py    # 医学图像处理工具
│   │   ├── coordinate_transform.py # 坐标系变换工具
│   │   ├── path_visualizer.py     # 路径可视化工具
│   │   ├── exportplan.py          # 规划结果导出工具
│   │   └── io_utils.py            # 文件输入输出工具
│   ├── cli/                       # 命令行接口
│   │   ├── __init__.py
│   │   └── business_cli.py        # 业务规划命令行工具
│   └── main.py                    # 主程序入口
├── docs/                          # 项目文档
│   ├── PROJECT_SUMMARY.md         # 项目总结
│   ├── algorithm_design.md        # 算法设计文档
│   ├── user_manual.md             # 用户使用手册
│   ├── development_progress.md    # 开发进度报告
│   ├── volumetric_optimization_guide.md # 体积优化指南
│   └── USAGE_GUIDE.md             # 使用指南
├── examples/                      # 示例和测试数据
│   ├── PlanData/                  # 规划数据示例
│   │   ├── CT.nii.gz              # 示例CT图像
│   │   ├── scapula_mask.nii.gz    # 肩胛骨掩膜
│   │   ├── screw_coordinates.json # 螺钉坐标数据
│   │   └── screw_specs.json       # 螺钉规格参数
│   ├── basic_planning_example.py  # 基础规划示例
│   └── business_planning_example.py # 业务规划示例
├── tests/                         # 测试模块
│   ├── diagnose_coordinates.py    # 坐标诊断工具
│   ├── diagnose_screw_path.py     # 路径诊断工具
│   └── find_valid_coordinates.py  # 有效坐标查找工具
├── papers/                        # 参考文献
│   └── Li 等 - 2022 - Automatic surgical planning...md
├── logs/                          # 日志文件目录
├── run_screw_planning.py          # 主程序启动脚本
├── run_business_planning.py       # 业务规划启动脚本
└── README.md                      # 项目说明文档
```

## ⚙️ 安装与配置

### 系统要求

- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python版本**：3.8 或更高版本

### 依赖安装

1. **克隆项目**：
```bash
git clone <repository-url>
cd ScrewPlanning
```

2. **创建虚拟环境**（推荐）：
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

3. **安装依赖包**：
```bash
pip install SimpleITK>=2.0.0
pip install numpy>=1.20.0
pip install scipy>=1.7.0
pip install vtk>=9.0.0
pip install matplotlib>=3.3.0
```

### 环境验证

运行以下命令验证环境配置：
```bash
python -c "import SimpleITK as sitk; import numpy as np; import vtk; print('环境配置成功！')"
```

## 🚀 使用说明

### 基本使用方法

#### 1. 传统规划模式

```python
from src.core.path_planning import PathPlanner
from src.core.bone_density import BoneDensityCalculator
from src.core.cone_space import ConeSpaceGenerator
from src.utils.io_utils import load_ct_image, load_mask_image

# 加载数据
ct_image = load_ct_image("path/to/ct.nii.gz")
mask_image = load_mask_image("path/to/mask.nii.gz")

# 初始化组件
bone_density_calc = BoneDensityCalculator(ct_image, mask_image)
cone_space_gen = ConeSpaceGenerator(
    screw_length=50.0,
    screw_radius=3.25,
    constraint_angle=45.0
)

# 创建规划器
planner = PathPlanner(bone_density_calc, cone_space_gen)

# 执行规划
reference_points = [p1, p2, p3, p4]  # 4个参考点
result = planner.plan_screw_paths(reference_points)

# 查看结果
if result.success:
    print(f"规划成功！找到 {len(result.optimal_paths)} 条最优路径")
    for i, path in enumerate(result.optimal_paths):
        print(f"路径 {i+1}: 骨密度积分 = {path.bone_density_integral:.2f}")
```

#### 2. 业务规划模式

```python
from src.core.business_planning import BusinessScrewPlanner

# 初始化业务规划器
business_planner = BusinessScrewPlanner(ct_image, mask_image)

# 设置螺钉起始坐标
screw_coords = {
    "center": [x1, y1, z1],
    "top": [x2, y2, z2],
    "bottom": [x3, y3, z3]
}

# 执行业务规划
results = business_planner.plan_all_screws(screw_coords)

# 输出结果
for screw_name, result in results.items():
    if result.success:
        optimal_path = result.optimal_paths[0]
        end_point = optimal_path.end_point
        print(f"{screw_name} 螺钉终点坐标: ({end_point.x:.2f}, {end_point.y:.2f}, {end_point.z:.2f})")
```

### 命令行使用

#### 业务规划命令行工具

```bash
# 基本用法
python -m src.cli.business_cli \
    --ct-path "examples/PlanData/CT.nii.gz" \
    --mask-path "examples/PlanData/scapula_mask.nii.gz" \
    --coords-path "examples/PlanData/business_screw_coords.json" \
    --output-dir "output/"

# 自定义参数
python -m src.cli.business_cli \
    --ct-path "data/CT.nii.gz" \
    --mask-path "data/mask.nii.gz" \
    --coords-path "data/coords.json" \
    --screw-length 45.0 \
    --screw-radius 3.0 \
    --constraint-angle 40.0 \
    --output-dir "results/"
```

### 示例脚本

项目提供了完整的示例脚本：

1. **基础规划示例**：
```bash
python examples/basic_planning_example.py
```

2. **业务规划示例**：
```bash
python examples/business_planning_example.py
```

### 参数配置

#### 关键算法参数

| 参数名称 | 默认值 | 说明 | 取值范围 |
|---------|--------|------|---------|
| `screw_length` | 50.0 mm | 螺钉长度 | 30-80 mm |
| `screw_radius` | 3.25 mm | 螺钉半径 | 2.0-5.0 mm |
| `constraint_angle` | 45.0° | 约束角度 | 30-60° |
| `radial_resolution` | 30 | 径向分辨率 | 10-50 |
| `circumferential_resolution` | 150 | 周向分辨率 | 50-300 |
| `integration_resolution` | 30 | 积分分辨率 | 20-50 |
| `coverage_threshold` | 0.95 | 覆盖率阈值 | 0.8-1.0 |

## 📊 结果可视化

### 规划结果展示

- 红色、蓝色的锥形范围：待选路径（螺钉中心线）范围
- 

![image-20250730105149232](./resource/imgs/image-20250730105149232.png)



### 结果输出格式

#### JSON格式输出
```json
{
  "success": true,
  "planning_time": 0.7293422222137451,
  "error_message": "",
  "screw_endpoints": {
    "top": {
      "x": -49.67834643722023,
      "y": 187.0443059812471,
      "z": 225.46694762741927
    },
    "bottom": {
      "x": -51.48638428152475,
      "y": 208.35167846309895,
      "z": 206.61222480690998
    }
  },
  "statistics": {
    "total_screws": 2,
    "successful_screws": 2,
    "base_normal": {
      "x": 0.9963203071509472,
      "y": 0.0857079083786465,
      "z": -0.0
    },
    "base_center": {
      "x": -70.03790000000001,
      "y": 195.678,
      "z": 215.6515
    }
  },
  "screw_paths": {
    "top": {
      "start_point": {
        "x": -69.6298,
        "y": 190.934,
        "z": 222.572
      },
      "end_point": {
        "x": -49.67834643722023,
        "y": 187.0443059812471,
        "z": 225.46694762741927
      },
      "length": 20.0,
      "radius": 3.25,
      "bone_density_integral": 5485.58594657908,
      "is_valid": true,
      "coverage_ratio": 0.0
    },
    "bottom": {
      "start_point": {
        "x": -70.446,
        "y": 200.422,
        "z": 208.731
      },
      "end_point": {
        "x": -51.48638428152475,
        "y": 208.35167846309895,
        "z": 206.61222480690998
      },
      "length": 20.0,
      "radius": 3.25,
      "bone_density_integral": 8200.853465112199,
      "is_valid": true,
      "coverage_ratio": 0.0
    }
  }
}
```

#### 字段含义解释
>  JSON文件记录了系统为肩关节手术规划的螺钉的完整信息。

1. `success`: true
   - **含义**：规划是否成功完
   - `true`：系统成功找到了合适的螺钉植入路径
   - `false`：规划失败，可能是骨质条件不佳或参数设置问题
   - **临床意义**：决定了这次规划结果是否可以用于手术指导

2. `planning_time`: 0.7293422222137451
   - **含义**：算法计算耗时
   - **单位**：秒
   - **取值范围**：通常0.5-30秒
   - **临床意义**：反映算法规划效率

3.  `error_message`: ""
   - **含义**：错误信息
   - 空字符串表示无错误
   - 如有错误会显示具体原因
   - **临床意义**：帮助医生了解规划失败的原因

4. 螺钉末端坐标 (`screw_endpoints`)

   > 这部分记录了两根螺钉的最终植入位置：

   - `top` 螺钉（上方螺钉）

   - `bottom` 螺钉（下方螺钉）

   - **坐标系统说明**：

     - **单位**：毫米（mm）

     - **参考框架**：CT图像的世界坐标系

     - **x轴**：左右方向（负值表示左侧）

     - **y轴**：前后方向（正值表示前方）

     - **z轴**：上下方向（正值表示上方）


   - **临床意义**：这些坐标告诉前端螺钉末端的指向位置。

5. 统计信息 (`statistics`)

   - `total_screws`: 2

   - **含义**：规划的螺钉总数
     - 肩盂假体通常需要2根螺钉固定


6. `successful_screws`: 2
   - **含义**：成功规划的螺钉数量
     - 与总数相等表示所有螺钉都找到了合适路径


7. `base_normal`（基座法向量）

   - **含义**：假体基座的朝向

     - 这是一个单位向量，表示假体表面的垂直方向

     - **临床意义**：确保假体与骨骼表面贴合良好


8. `base_center`（基座中心）

   - **含义**：假体基座的中心位置

     - **单位**：毫米（mm）

     - **临床意义**：假体安装的参考点


9.  详细螺钉路径信息 (`screw_paths`)

每根螺钉都包含以下详细信息：

### `start_point`（起始点）
螺钉开始钻入的位置，即假体表面的入点

### `end_point`（终止点）
螺钉钻入的最深位置，与`screw_endpoints`中的坐标相同

### `length`: 20.0
**含义**：螺钉的有效长度
- **单位**：毫米（mm）
- **临床意义**：实际植入骨骼内的螺钉长度

### `radius`: 3.25
**含义**：螺钉半径
- **单位**：毫米（mm）
- 对应直径6.5mm的螺钉规格

### `bone_density_integral`
- **top螺钉**：5485.59
- **bottom螺钉**：8200.85

**含义**：沿螺钉路径的骨密度积分值
- **单位**：HU·mm（亨氏单位×毫米）
- **取值范围**：通常3000-15000
- **临床意义**：
  - 数值越高表示路径上骨质越密实
  - bottom螺钉数值更高，说明下方骨质条件更好
  - 高骨密度积分预示更好的螺钉固定强度

### `is_valid`: true
**含义**：路径是否有效
- 确认螺钉完全位于骨骼内部，不会穿出骨骼表面

### `coverage_ratio`: 0.0
**含义**：螺钉表面与骨骼接触的覆盖率
- **取值范围**：0.0-1.0
- 0.0可能表示使用了简化计算模式

## 🏥 临床应用价值

### 手术指导意义
1. **精确定位**：坐标信息帮助医生精确定位螺钉植入点
2. **质量评估**：骨密度积分帮助评估固定强度
3. **安全保障**：`is_valid`确认螺钉不会意外穿出骨骼
4. **手术规划**：完整的路径信息支持术前模拟

### 结果解读
- **两根螺钉都规划成功**：可以进行手术
- **bottom螺钉骨密度更高**：下方固定点更可靠
- **规划时间短**：系统效率高，适合临床使用

这个结果文件为外科医生提供了完整的螺钉植入指导信息，确保手术的安全性和成功率。





## 📖 参考文献

### 主要参考论文

**Li, H., Xu, J., Zhang, D., He, Y., & Chen, X. (2022).** *Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty.* **International Journal of Computer Assisted Radiology and Surgery**, 17(8), 1535-1546.

**DOI**: [10.1007/s11548-022-02639-1](https://doi.org/10.1007/s11548-022-02639-1)

![Li 等 - 2022 - Automatic surgical planning based on bone density assessment and path integral in cone space for rev_03](./resource/imgs/Li 等 - 2022 - Automatic surgical planning based on bone density assessment and path integral in cone space for rev_03.png)