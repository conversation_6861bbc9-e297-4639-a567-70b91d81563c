"""
路径优化模块

提供螺钉路径的后处理优化功能，包括传统加权评分和Pareto最优化
"""

import numpy as np
import math
from typing import List, Tuple, Optional, Dict, Any
import logging
from dataclasses import dataclass

try:
    from ..utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult
except ImportError:
    from utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult


@dataclass
class MultiObjectiveScore:
    """多目标评分数据类"""
    bone_density: float  # 骨密度积分（归一化后）
    coverage_ratio: float  # 覆盖率
    safety_score: float  # 安全性评分（1 - 角度偏差）

    def to_array(self) -> np.ndarray:
        """转换为numpy数组用于计算"""
        return np.array([self.bone_density, self.coverage_ratio, self.safety_score])

    def __str__(self) -> str:
        return f"骨密度:{self.bone_density:.3f}, 覆盖率:{self.coverage_ratio:.3f}, 安全性:{self.safety_score:.3f}"


@dataclass
class ParetoSolution:
    """Pareto最优解数据类"""
    path: ScrewPath
    objectives: MultiObjectiveScore
    rank: int = 0  # Pareto排名
    crowding_distance: float = 0.0  # 拥挤距离

    def __str__(self) -> str:
        return f"Rank:{self.rank}, 拥挤距离:{self.crowding_distance:.3f}, 目标:{self.objectives}"


class PathOptimizer:
    """路径优化器"""
    
    def __init__(self):
        """初始化路径优化器"""
        logging.info("路径优化器初始化完成")
    
    def optimize_path_pair(self, path1: ScrewPath, path2: ScrewPath,
                          target_angle: float = 45.0,
                          weight_density: float = 0.7,
                          weight_angle: float = 0.3) -> Tuple[ScrewPath, ScrewPath]:
        """
        优化螺钉路径对
        
        Args:
            path1: 螺钉路径1
            path2: 螺钉路径2
            target_angle: 目标夹角（度）
            weight_density: 骨密度权重
            weight_angle: 角度权重
            
        Returns:
            优化后的路径对
        """
        # 计算当前夹角
        dir1 = path1.get_direction_vector()
        dir2 = path2.get_direction_vector()
        current_angle = self.calculate_angle_between_vectors(dir1, dir2)
        
        # 计算角度偏差
        angle_deviation = abs(current_angle - math.radians(target_angle))
        
        # 计算综合评分
        density_score1 = path1.bone_density_integral
        density_score2 = path2.bone_density_integral
        angle_score = 1.0 / (1.0 + angle_deviation)  # 角度越接近目标，评分越高
        
        combined_score = (weight_density * (density_score1 + density_score2) + 
                         weight_angle * angle_score)
        
        # 更新路径评分
        path1.safety_score = combined_score
        path2.safety_score = combined_score
        
        logging.info(f"路径对优化完成 - 夹角: {math.degrees(current_angle):.1f}°, "
                    f"综合评分: {combined_score:.2f}")
        
        return path1, path2
    
    def calculate_angle_between_vectors(self, v1: Vector3D, v2: Vector3D) -> float:
        """
        计算两个向量之间的夹角
        
        Args:
            v1: 向量1
            v2: 向量2
            
        Returns:
            夹角（弧度）
        """
        dot_product = v1.dot(v2)
        magnitude_product = v1.magnitude() * v2.magnitude()
        
        if magnitude_product == 0:
            return 0
        
        cos_angle = dot_product / magnitude_product
        # 限制在[-1, 1]范围内，避免数值误差
        cos_angle = max(-1.0, min(1.0, cos_angle))
        
        return math.acos(abs(cos_angle))
    
    def filter_paths_by_stability(self, paths: List[ScrewPath],
                                 min_bone_density: float = 100.0,
                                 min_path_length: float = 30.0) -> List[ScrewPath]:
        """
        根据稳定性标准过滤路径
        
        Args:
            paths: 候选路径列表
            min_bone_density: 最小骨密度积分
            min_path_length: 最小路径长度
            
        Returns:
            过滤后的路径列表
        """
        filtered_paths = []
        
        for path in paths:
            # 检查骨密度积分
            if path.bone_density_integral < min_bone_density:
                continue
            
            # 检查路径长度
            if path.get_path_length() < min_path_length:
                continue
            
            # 检查路径有效性
            if not path.is_valid:
                continue
            
            filtered_paths.append(path)
        
        logging.info(f"稳定性过滤: {len(paths)} -> {len(filtered_paths)}")
        return filtered_paths
    
    def rank_paths_by_multiple_criteria(self, paths: List[ScrewPath],
                                      weight_density: float = 0.5,
                                      weight_length: float = 0.3,
                                      weight_safety: float = 0.2) -> List[ScrewPath]:
        """
        根据多个标准对路径进行排序
        
        Args:
            paths: 路径列表
            weight_density: 骨密度权重
            weight_length: 路径长度权重
            weight_safety: 安全性权重
            
        Returns:
            排序后的路径列表
        """
        if not paths:
            return paths
        
        # 归一化各项指标
        densities = [path.bone_density_integral for path in paths]
        lengths = [path.get_path_length() for path in paths]
        safety_scores = [path.safety_score for path in paths]
        
        max_density = max(densities) if densities else 1.0
        max_length = max(lengths) if lengths else 1.0
        max_safety = max(safety_scores) if safety_scores else 1.0
        
        # 计算综合评分
        for i, path in enumerate(paths):
            normalized_density = densities[i] / max_density if max_density > 0 else 0
            normalized_length = lengths[i] / max_length if max_length > 0 else 0
            normalized_safety = safety_scores[i] / max_safety if max_safety > 0 else 0
            
            combined_score = (weight_density * normalized_density +
                            weight_length * normalized_length +
                            weight_safety * normalized_safety)
            
            path.safety_score = combined_score
        
        # 按综合评分排序
        sorted_paths = sorted(paths, key=lambda p: p.safety_score, reverse=True)
        
        logging.info(f"路径排序完成，最高评分: {sorted_paths[0].safety_score:.3f}")
        return sorted_paths
    
    def optimize_planning_result(self, result: PlanningResult,
                               target_angle: float = 45.0) -> PlanningResult:
        """
        优化规划结果
        
        Args:
            result: 原始规划结果
            target_angle: 目标夹角（度）
            
        Returns:
            优化后的规划结果
        """
        if not result.success or len(result.optimal_paths) < 2:
            return result
        
        try:
            # 对最优路径对进行优化
            if len(result.optimal_paths) >= 2:
                path1, path2 = result.optimal_paths[0], result.optimal_paths[1]
                optimized_path1, optimized_path2 = self.optimize_path_pair(
                    path1, path2, target_angle)
                
                result.optimal_paths[0] = optimized_path1
                result.optimal_paths[1] = optimized_path2
            
            # 对所有候选路径进行稳定性过滤
            if result.all_candidate_paths:
                filtered_paths = self.filter_paths_by_stability(
                    result.all_candidate_paths)
                
                # 重新排序
                ranked_paths = self.rank_paths_by_multiple_criteria(filtered_paths)
                result.all_candidate_paths = ranked_paths
                
                # 更新统计信息
                result.statistics['valid_candidates'] = len(filtered_paths)
            
            logging.info("规划结果优化完成")
            
        except Exception as e:
            logging.error(f"规划结果优化过程中出错: {e}")
        
        return result
    
    def calculate_path_interference(self, path1: ScrewPath, path2: ScrewPath,
                                  safety_margin: float = 2.0) -> bool:
        """
        检查两条路径是否存在干涉
        
        Args:
            path1: 螺钉路径1
            path2: 螺钉路径2
            safety_margin: 安全边距 (mm)
            
        Returns:
            True如果存在干涉，False否则
        """
        # 简化的干涉检查：检查路径上采样点之间的最小距离
        points1 = path1.get_points_along_path(20)
        points2 = path2.get_points_along_path(20)
        
        min_distance = float('inf')
        
        for p1 in points1:
            for p2 in points2:
                distance = p1.distance_to(p2)
                min_distance = min(min_distance, distance)
        
        # 考虑螺钉半径和安全边距
        required_clearance = (path1.radius + path2.radius + safety_margin)
        
        return min_distance < required_clearance
    
    def adjust_path_for_clearance(self, path: ScrewPath, 
                                obstacle_paths: List[ScrewPath],
                                adjustment_step: float = 1.0) -> Optional[ScrewPath]:
        """
        调整路径以避免干涉
        
        Args:
            path: 需要调整的路径
            obstacle_paths: 障碍路径列表
            adjustment_step: 调整步长 (mm)
            
        Returns:
            调整后的路径，如果无法调整则返回None
        """
        # 这是一个简化的实现，实际应用中可能需要更复杂的算法
        original_end = path.end_point
        direction = path.get_direction_vector()
        
        # 尝试在垂直方向上微调终点
        perpendicular = Vector3D(direction.y, -direction.x, 0).normalize()
        
        for offset in [-adjustment_step, adjustment_step]:
            adjusted_end_array = (original_end.to_array() + 
                                offset * perpendicular.to_array())
            adjusted_end = Point3D.from_array(adjusted_end_array)
            
            adjusted_path = ScrewPath(path.start_point, adjusted_end, 
                                    path.radius, path.length)
            
            # 检查调整后的路径是否仍有干涉
            has_interference = any(self.calculate_path_interference(adjusted_path, obs_path)
                                 for obs_path in obstacle_paths)
            
            if not has_interference:
                logging.info(f"路径调整成功，偏移: {offset}mm")
                return adjusted_path
        
        logging.warning("无法通过调整消除路径干涉")
        return None
    
    def validate_planning_constraints(self, result: PlanningResult,
                                    max_angle: float = 45.0,
                                    min_bone_density: float = 100.0) -> bool:
        """
        验证规划结果是否满足约束条件
        
        Args:
            result: 规划结果
            max_angle: 最大允许角度（度）
            min_bone_density: 最小骨密度积分
            
        Returns:
            True如果满足约束，False否则
        """
        if not result.success or not result.optimal_paths:
            return False
        
        # 检查骨密度约束
        for path in result.optimal_paths:
            if path.bone_density_integral < min_bone_density:
                logging.warning(f"路径骨密度不足: {path.bone_density_integral} < {min_bone_density}")
                return False
        
        # 检查角度约束（如果有多条路径）
        if len(result.optimal_paths) >= 2:
            for i in range(len(result.optimal_paths)):
                for j in range(i + 1, len(result.optimal_paths)):
                    path1, path2 = result.optimal_paths[i], result.optimal_paths[j]
                    angle = self.calculate_angle_between_vectors(
                        path1.get_direction_vector(), path2.get_direction_vector())
                    
                    if math.degrees(angle) > max_angle:
                        logging.warning(f"路径夹角超出约束: {math.degrees(angle):.1f}° > {max_angle}°")
                        return False
        
        logging.info("规划结果满足所有约束条件")
        return True


class ParetoOptimizer:
    """Pareto最优化器"""

    def __init__(self, target_angle: float = 0.0):
        """
        初始化Pareto优化器

        Args:
            target_angle: 目标角度（度）。0度表示与参考方向完全一致，
                         15度表示最大允许偏差
        """
        self.target_angle = math.radians(target_angle)
        logging.info(f"Pareto优化器初始化完成，目标角度: {target_angle}°")

    def calculate_multi_objective_scores(self, paths: List[ScrewPath],
                                       reference_direction: Optional[Vector3D] = None) -> List[MultiObjectiveScore]:
        """
        计算多目标评分

        Args:
            paths: 路径列表
            reference_direction: 参考方向向量（用于角度计算）

        Returns:
            多目标评分列表
        """
        if not paths:
            return []

        scores = []

        # 提取原始指标
        bone_densities = [path.bone_density_integral for path in paths]
        coverage_ratios = [path.coverage_ratio for path in paths]

        # 归一化骨密度积分
        max_density = max(bone_densities) if bone_densities else 1.0
        min_density = min(bone_densities) if bone_densities else 0.0
        density_range = max_density - min_density if max_density > min_density else 1.0

        for i, path in enumerate(paths):
            # 目标1: 归一化骨密度积分（最大化）
            normalized_density = (bone_densities[i] - min_density) / density_range

            # 目标2: 覆盖率（最大化）
            coverage = coverage_ratios[i]

            # 目标3: 安全性评分（基于角度偏差，最大化）
            safety = 1.0  # 默认最高安全性
            if reference_direction is not None:
                path_direction = path.get_direction_vector()
                angle = self._calculate_angle_between_vectors(path_direction, reference_direction)
                angle_deviation = abs(angle - self.target_angle)

                # 15度约束：超过15度的路径安全性为0
                max_allowed_deviation = math.radians(15.0)  # 15度转换为弧度

                if angle_deviation > max_allowed_deviation:
                    safety = 0.0  # 超出约束范围，完全不安全
                else:
                    # 在15度范围内，线性评分：偏差越小，安全性越高
                    safety = 1.0 - (angle_deviation / max_allowed_deviation)

            score = MultiObjectiveScore(
                bone_density=normalized_density,
                coverage_ratio=coverage,
                safety_score=safety
            )
            scores.append(score)

        return scores

    def _calculate_angle_between_vectors(self, v1: Vector3D, v2: Vector3D) -> float:
        """计算两个向量之间的夹角"""
        dot_product = v1.dot(v2)
        magnitude1 = v1.magnitude()
        magnitude2 = v2.magnitude()

        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        cos_angle = dot_product / (magnitude1 * magnitude2)
        cos_angle = max(-1.0, min(1.0, cos_angle))  # 确保在有效范围内
        return math.acos(cos_angle)

    def non_dominated_sort(self, solutions: List[ParetoSolution]) -> List[List[ParetoSolution]]:
        """
        非支配排序算法

        Args:
            solutions: Pareto解列表

        Returns:
            按Pareto等级分组的解列表
        """
        n = len(solutions)
        if n == 0:
            return []

        # 初始化
        domination_count = [0] * n  # 被支配次数
        dominated_solutions = [[] for _ in range(n)]  # 支配的解
        fronts = [[]]  # Pareto前沿

        # 计算支配关系
        for i in range(n):
            for j in range(n):
                if i != j:
                    if self._dominates(solutions[i], solutions[j]):
                        dominated_solutions[i].append(j)
                    elif self._dominates(solutions[j], solutions[i]):
                        domination_count[i] += 1

            # 如果不被任何解支配，则属于第一前沿
            if domination_count[i] == 0:
                solutions[i].rank = 1
                fronts[0].append(i)

        # 构建后续前沿
        front_index = 0
        while len(fronts[front_index]) > 0:
            next_front = []
            for i in fronts[front_index]:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        solutions[j].rank = front_index + 2
                        next_front.append(j)

            front_index += 1
            fronts.append(next_front)

        # 转换为解对象列表
        result_fronts = []
        for front_indices in fronts[:-1]:  # 排除最后一个空前沿
            front_solutions = [solutions[i] for i in front_indices]
            result_fronts.append(front_solutions)

        return result_fronts

    def _dominates(self, sol1: ParetoSolution, sol2: ParetoSolution) -> bool:
        """
        判断sol1是否支配sol2

        Args:
            sol1: 解1
            sol2: 解2

        Returns:
            True如果sol1支配sol2
        """
        obj1 = sol1.objectives.to_array()
        obj2 = sol2.objectives.to_array()

        # 检查是否在所有目标上都不劣于sol2，且至少在一个目标上优于sol2
        better_in_any = False
        for i in range(len(obj1)):
            if obj1[i] < obj2[i]:  # 在某个目标上劣于sol2
                return False
            elif obj1[i] > obj2[i]:  # 在某个目标上优于sol2
                better_in_any = True

        return better_in_any

    def calculate_crowding_distance(self, solutions: List[ParetoSolution]) -> None:
        """
        计算拥挤距离

        Args:
            solutions: 同一Pareto等级的解列表
        """
        n = len(solutions)
        if n <= 2:
            # 边界解设置为无穷大
            for sol in solutions:
                sol.crowding_distance = float('inf')
            return

        # 初始化拥挤距离
        for sol in solutions:
            sol.crowding_distance = 0.0

        # 对每个目标函数计算拥挤距离
        objectives = solutions[0].objectives.to_array()
        num_objectives = len(objectives)

        for obj_idx in range(num_objectives):
            # 按当前目标函数值排序
            solutions.sort(key=lambda x: x.objectives.to_array()[obj_idx])

            # 边界解设置为无穷大
            solutions[0].crowding_distance = float('inf')
            solutions[-1].crowding_distance = float('inf')

            # 计算目标函数的范围
            obj_values = [sol.objectives.to_array()[obj_idx] for sol in solutions]
            obj_range = max(obj_values) - min(obj_values)

            if obj_range == 0:
                continue

            # 计算中间解的拥挤距离
            for i in range(1, n - 1):
                if solutions[i].crowding_distance != float('inf'):
                    distance = (obj_values[i + 1] - obj_values[i - 1]) / obj_range
                    solutions[i].crowding_distance += distance

    def optimize_paths_pareto(self, paths: List[ScrewPath],
                            reference_direction: Optional[Vector3D] = None,
                            max_solutions: int = 10) -> List[ParetoSolution]:
        """
        使用Pareto最优化选择路径

        Args:
            paths: 候选路径列表
            reference_direction: 参考方向向量
            max_solutions: 最大返回解数量

        Returns:
            Pareto最优解列表
        """
        if not paths:
            return []

        logging.info(f"开始Pareto最优化，候选路径数: {len(paths)}")

        # 计算多目标评分
        scores = self.calculate_multi_objective_scores(paths, reference_direction)

        # 创建Pareto解
        solutions = []
        for i, (path, score) in enumerate(zip(paths, scores)):
            solution = ParetoSolution(path=path, objectives=score)
            solutions.append(solution)

        # 非支配排序
        fronts = self.non_dominated_sort(solutions)

        if not fronts:
            logging.warning("未找到任何Pareto前沿")
            return []

        # 计算拥挤距离并选择解
        pareto_solutions = []
        for front in fronts:
            if len(pareto_solutions) >= max_solutions:
                break

            # 计算当前前沿的拥挤距离
            self.calculate_crowding_distance(front)

            # 按拥挤距离排序（降序）
            front.sort(key=lambda x: x.crowding_distance, reverse=True)

            # 添加解直到达到最大数量
            remaining_slots = max_solutions - len(pareto_solutions)
            pareto_solutions.extend(front[:remaining_slots])

        logging.info(f"Pareto优化完成，返回 {len(pareto_solutions)} 个最优解")
        logging.info(f"第一前沿解数量: {len(fronts[0]) if fronts else 0}")

        return pareto_solutions
