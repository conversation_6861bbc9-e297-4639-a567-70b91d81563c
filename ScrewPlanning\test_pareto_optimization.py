#!/usr/bin/env python3
"""
测试Pareto最优化功能
"""

import sys
import os
import logging

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from core.optimization import ParetoOptimizer, MultiObjectiveScore, ParetoSolution
from utils.geometry import ScrewPath, Point3D, Vector3D

def test_pareto_optimization():
    """测试Pareto最优化功能"""
    print("🧪 测试Pareto最优化功能")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试路径
    test_paths = []
    
    # 路径1: 高骨密度，低覆盖率
    path1 = ScrewPath(Point3D(0, 0, 0), Point3D(0, 0, 50), 3.25, 50)
    path1.bone_density_integral = 1000.0
    path1.coverage_ratio = 0.85
    path1.is_valid = False  # 覆盖率不足95%
    
    # 路径2: 中等骨密度，高覆盖率
    path2 = ScrewPath(Point3D(1, 0, 0), Point3D(1, 0, 50), 3.25, 50)
    path2.bone_density_integral = 800.0
    path2.coverage_ratio = 0.98
    path2.is_valid = True
    
    # 路径3: 低骨密度，高覆盖率
    path3 = ScrewPath(Point3D(2, 0, 0), Point3D(2, 0, 50), 3.25, 50)
    path3.bone_density_integral = 600.0
    path3.coverage_ratio = 0.96
    path3.is_valid = True
    
    # 路径4: 高骨密度，高覆盖率（理想解）
    path4 = ScrewPath(Point3D(3, 0, 0), Point3D(3, 0, 50), 3.25, 50)
    path4.bone_density_integral = 950.0
    path4.coverage_ratio = 0.97
    path4.is_valid = True
    
    # 路径5: 中等骨密度，中等覆盖率
    path5 = ScrewPath(Point3D(4, 0, 0), Point3D(4, 0, 50), 3.25, 50)
    path5.bone_density_integral = 750.0
    path5.coverage_ratio = 0.95
    path5.is_valid = True
    
    test_paths = [path1, path2, path3, path4, path5]
    
    # 只使用有效路径进行Pareto优化
    valid_paths = [path for path in test_paths if path.is_valid]
    
    print(f"📊 测试数据:")
    for i, path in enumerate(valid_paths):
        print(f"  路径{i+1}: 骨密度={path.bone_density_integral:.1f}, 覆盖率={path.coverage_ratio:.3f}")
    
    # 创建Pareto优化器
    optimizer = ParetoOptimizer(target_angle=45.0)
    
    # 执行Pareto优化
    reference_direction = Vector3D(0, 0, 1)  # Z轴方向
    pareto_solutions = optimizer.optimize_paths_pareto(
        valid_paths, reference_direction, max_solutions=10)
    
    print(f"\n🎯 Pareto优化结果:")
    print(f"找到 {len(pareto_solutions)} 个Pareto最优解")
    
    for i, solution in enumerate(pareto_solutions):
        print(f"\n解{i+1}:")
        print(f"  Pareto等级: {solution.rank}")
        print(f"  拥挤距离: {solution.crowding_distance:.3f}")
        print(f"  目标函数值: {solution.objectives}")
        print(f"  原始骨密度: {solution.path.bone_density_integral:.1f}")
        print(f"  原始覆盖率: {solution.path.coverage_ratio:.3f}")
    
    # 测试多目标评分计算
    print(f"\n📈 多目标评分测试:")
    scores = optimizer.calculate_multi_objective_scores(valid_paths, reference_direction)
    for i, (path, score) in enumerate(zip(valid_paths, scores)):
        print(f"  路径{i+1}: {score}")
    
    print("\n✅ Pareto优化测试完成!")

if __name__ == "__main__":
    test_pareto_optimization()
