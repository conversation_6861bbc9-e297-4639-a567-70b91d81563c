#!/usr/bin/env python3
"""
检查基座坐标和肩胛骨掩膜的空间关系

分析基座坐标相对于肩胛骨掩膜的位置
"""

import sys
import os
import logging
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def check_spatial_relationship():
    """检查空间关系"""
    try:
        # 导入必要的模块
        from core.bone_density import BoneDensityCalculator
        from utils.geometry import Point3D
        from utils.io_utils import DataLoader
        import SimpleITK as sitk
        
        print("=" * 60)
        print("空间关系检查工具")
        print("=" * 60)
        
        # 加载数据
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        
        # 创建骨密度计算器
        calculator = BoneDensityCalculator(ct_image, mask_image)
        
        # 获取图像信息
        print("📊 图像信息:")
        print(f"  CT图像尺寸: {calculator.ct_array.shape}")
        print(f"  体素间距: {calculator.spacing}")
        print(f"  图像原点: {calculator.origin}")
        
        # 获取掩膜边界
        mask_array = calculator.mask_array
        valid_indices = np.where(mask_array > 0)
        
        if len(valid_indices[0]) == 0:
            print("❌ 掩膜中没有有效区域")
            return False
        
        # 计算掩膜的物理坐标边界
        min_i, max_i = valid_indices[0].min(), valid_indices[0].max()
        min_j, max_j = valid_indices[1].min(), valid_indices[1].max()
        min_k, max_k = valid_indices[2].min(), valid_indices[2].max()
        
        print(f"\n📍 掩膜边界（图像坐标）:")
        print(f"  i: {min_i} - {max_i}")
        print(f"  j: {min_j} - {max_j}")
        print(f"  k: {min_k} - {max_k}")
        
        # 转换为物理坐标
        corners = [
            [min_k, min_j, min_i],  # 注意：SimpleITK使用(x,y,z)，numpy使用(z,y,x)
            [max_k, min_j, min_i],
            [min_k, max_j, min_i],
            [min_k, min_j, max_i],
            [max_k, max_j, max_i],
        ]
        
        print(f"\n📍 掩膜边界（物理坐标）:")
        for i, corner in enumerate(corners):
            try:
                physical_point = ct_image.TransformIndexToPhysicalPoint(corner)
                print(f"  角点{i+1}: ({physical_point[0]:.1f}, {physical_point[1]:.1f}, {physical_point[2]:.1f})")
            except Exception as e:
                print(f"  角点{i+1}: 转换失败 - {e}")
        
        # 定义基座坐标
        screw_coordinates = {
            'center': Point3D(70.0379, -195.678, 215.651),
            'top': Point3D(69.6298, -190.934, 222.572),
            'bottom': Point3D(70.446, -200.422, 208.731)
        }
        
        print(f"\n📍 基座坐标:")
        for name, coord in screw_coordinates.items():
            print(f"  {name}: ({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f})")
            
            # 转换为图像坐标
            try:
                i, j, k = calculator.world_to_image_coordinates(coord)
                print(f"    图像坐标: ({i}, {j}, {k})")
                
                # 检查是否在图像范围内
                in_bounds = (0 <= i < mask_array.shape[0] and
                           0 <= j < mask_array.shape[1] and
                           0 <= k < mask_array.shape[2])
                print(f"    在图像范围内: {'是' if in_bounds else '否'}")
                
                if in_bounds:
                    mask_value = mask_array[i, j, k]
                    print(f"    掩膜值: {mask_value}")
                    
                    # 检查周围区域
                    print(f"    周围掩膜检查:")
                    found_mask = False
                    for di in range(-10, 11, 5):
                        for dj in range(-10, 11, 5):
                            for dk in range(-10, 11, 5):
                                ni, nj, nk = i + di, j + dj, k + dk
                                if (0 <= ni < mask_array.shape[0] and
                                    0 <= nj < mask_array.shape[1] and
                                    0 <= nk < mask_array.shape[2]):
                                    if mask_array[ni, nj, nk] > 0:
                                        distance = np.sqrt(di*di + dj*dj + dk*dk)
                                        print(f"      找到掩膜在偏移({di},{dj},{dk})，距离: {distance:.1f}")
                                        found_mask = True
                                        break
                            if found_mask:
                                break
                        if found_mask:
                            break
                    
                    if not found_mask:
                        print(f"      周围10个体素内未找到掩膜")
                
            except Exception as e:
                print(f"    坐标转换失败: {e}")
        
        # 寻找最近的掩膜点
        print(f"\n🔍 寻找最近的掩膜点:")
        center = screw_coordinates['center']
        
        try:
            center_i, center_j, center_k = calculator.world_to_image_coordinates(center)
            
            min_distance = float('inf')
            closest_point = None
            
            # 在掩膜中采样一些点
            sample_indices = np.random.choice(len(valid_indices[0]), min(1000, len(valid_indices[0])), replace=False)
            
            for idx in sample_indices:
                mask_i = valid_indices[0][idx]
                mask_j = valid_indices[1][idx]
                mask_k = valid_indices[2][idx]
                
                # 转换为物理坐标
                physical_point = ct_image.TransformIndexToPhysicalPoint([int(mask_k), int(mask_j), int(mask_i)])
                mask_coord = Point3D(physical_point[0], physical_point[1], physical_point[2])
                
                # 计算距离
                distance = np.sqrt((center.x - mask_coord.x)**2 + 
                                 (center.y - mask_coord.y)**2 + 
                                 (center.z - mask_coord.z)**2)
                
                if distance < min_distance:
                    min_distance = distance
                    closest_point = mask_coord
            
            if closest_point:
                print(f"  最近掩膜点: ({closest_point.x:.1f}, {closest_point.y:.1f}, {closest_point.z:.1f})")
                print(f"  距离: {min_distance:.1f} mm")
                
                # 计算方向向量
                direction_x = closest_point.x - center.x
                direction_y = closest_point.y - center.y
                direction_z = closest_point.z - center.z
                length = np.sqrt(direction_x**2 + direction_y**2 + direction_z**2)
                
                if length > 0:
                    direction_x /= length
                    direction_y /= length
                    direction_z /= length
                    
                    print(f"  建议方向: ({direction_x:.3f}, {direction_y:.3f}, {direction_z:.3f})")
                    
                    # 保存建议方向
                    import json
                    output_file = os.path.join(data_dir, 'suggested_direction.json')
                    output_data = {
                        'direction': {
                            'x': direction_x,
                            'y': direction_y,
                            'z': direction_z
                        },
                        'distance_to_mask': min_distance,
                        'closest_mask_point': {
                            'x': closest_point.x,
                            'y': closest_point.y,
                            'z': closest_point.z
                        }
                    }
                    
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(output_data, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 建议方向已保存到: {output_file}")
            
        except Exception as e:
            print(f"  寻找最近点失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = check_spatial_relationship()
    
    if success:
        print("\n🎉 空间关系检查完成！")
    else:
        print("\n❌ 空间关系检查失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
