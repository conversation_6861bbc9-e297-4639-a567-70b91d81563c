#!/usr/bin/env python3
"""
分析基座方向和螺钉植入方向

帮助理解基座坐标分布和正确的植入方向
"""

import sys
import os
import logging
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def analyze_direction():
    """分析方向"""
    try:
        # 导入必要的模块
        from core.bone_density import BoneDensityCalculator
        from utils.geometry import Point3D, Vector3D
        from utils.io_utils import DataLoader
        
        print("=" * 60)
        print("基座方向分析工具")
        print("=" * 60)
        
        # 加载数据
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        
        # 创建骨密度计算器
        calculator = BoneDensityCalculator(ct_image, mask_image)
        
        # 定义螺钉坐标
        screw_coordinates = {
            'center': Point3D(70.0379, -195.678, 215.651),
            'top': Point3D(69.6298, -190.934, 222.572),
            'bottom': Point3D(70.446, -200.422, 208.731)
        }
        
        print("📍 螺钉坐标分析:")
        print("-" * 60)
        
        center = screw_coordinates['center']
        top = screw_coordinates['top']
        bottom = screw_coordinates['bottom']
        
        print(f"中心: ({center.x:.3f}, {center.y:.3f}, {center.z:.3f})")
        print(f"上方: ({top.x:.3f}, {top.y:.3f}, {top.z:.3f})")
        print(f"下方: ({bottom.x:.3f}, {bottom.y:.3f}, {bottom.z:.3f})")
        
        # 计算向量
        vector1 = top - center
        vector2 = bottom - center
        
        print(f"\n📐 向量分析:")
        print(f"中心->上方: ({vector1.x:.3f}, {vector1.y:.3f}, {vector1.z:.3f})")
        print(f"中心->下方: ({vector2.x:.3f}, {vector2.y:.3f}, {vector2.z:.3f})")
        
        # 计算法向量
        normal = vector1.cross(vector2).normalize()
        print(f"法向量: ({normal.x:.3f}, {normal.y:.3f}, {normal.z:.3f})")
        
        # 测试多个方向
        test_directions = [
            ("原始法向量", normal),
            ("反向法向量", normal * -1),
            ("向内方向1", Vector3D(-1, 0, 0)),
            ("向内方向2", Vector3D(0, 1, 0)),
            ("向内方向3", Vector3D(0, 0, -1)),
            ("向内方向4", Vector3D(-0.5, 0.5, -0.5).normalize()),
        ]
        
        print(f"\n🔍 方向测试:")
        print("-" * 60)
        
        for name, direction in test_directions:
            print(f"\n{name}: ({direction.x:.3f}, {direction.y:.3f}, {direction.z:.3f})")
            
            # 测试这个方向是否能进入掩膜
            can_enter = False
            entry_distance = None
            
            for distance in [5, 10, 15, 20, 25, 30, 35, 40, 45, 50]:
                test_point_array = center.to_array() + direction.to_array() * distance
                test_point = Point3D.from_array(test_point_array)
                
                if calculator.is_point_in_mask(test_point):
                    can_enter = True
                    entry_distance = distance
                    break
            
            if can_enter:
                print(f"  ✅ 可以进入掩膜，距离: {entry_distance}mm")
                
                # 计算这个方向上的骨密度
                total_density = 0.0
                valid_samples = 0
                
                for distance in range(int(entry_distance), 51, 2):
                    test_point_array = center.to_array() + direction.to_array() * distance
                    test_point = Point3D.from_array(test_point_array)
                    
                    density = calculator.get_bone_density_at_point(test_point)
                    if density is not None:
                        total_density += density
                        valid_samples += 1
                
                if valid_samples > 0:
                    avg_density = total_density / valid_samples
                    print(f"  📊 平均骨密度: {avg_density:.1f} mg/cc")
                else:
                    print(f"  ⚠️  无法获取骨密度")
            else:
                print(f"  ❌ 无法进入掩膜")
        
        # 寻找最佳方向
        print(f"\n🎯 寻找最佳植入方向:")
        print("-" * 60)
        
        best_direction = None
        best_score = 0
        
        # 在球面上采样多个方向
        for theta in np.linspace(0, 2*np.pi, 36):  # 36个方位角
            for phi in np.linspace(0, np.pi, 18):   # 18个极角
                x = np.sin(phi) * np.cos(theta)
                y = np.sin(phi) * np.sin(theta)
                z = np.cos(phi)
                
                direction = Vector3D(x, y, z)
                
                # 测试这个方向
                can_enter = False
                entry_distance = None
                
                for distance in [5, 10, 15, 20, 25, 30]:
                    test_point_array = center.to_array() + direction.to_array() * distance
                    test_point = Point3D.from_array(test_point_array)
                    
                    if calculator.is_point_in_mask(test_point):
                        can_enter = True
                        entry_distance = distance
                        break
                
                if can_enter:
                    # 计算骨密度评分
                    total_density = 0.0
                    valid_samples = 0
                    
                    for distance in range(int(entry_distance), 51, 3):
                        test_point_array = center.to_array() + direction.to_array() * distance
                        test_point = Point3D.from_array(test_point_array)
                        
                        density = calculator.get_bone_density_at_point(test_point)
                        if density is not None:
                            total_density += density
                            valid_samples += 1
                    
                    if valid_samples > 0:
                        score = total_density / valid_samples
                        if score > best_score:
                            best_score = score
                            best_direction = direction
        
        if best_direction:
            print(f"✅ 找到最佳方向: ({best_direction.x:.3f}, {best_direction.y:.3f}, {best_direction.z:.3f})")
            print(f"📊 最佳评分: {best_score:.1f} mg/cc")
            
            # 保存最佳方向
            import json
            output_file = os.path.join(data_dir, 'optimal_direction.json')
            output_data = {
                'direction': {
                    'x': best_direction.x,
                    'y': best_direction.y,
                    'z': best_direction.z
                },
                'score': best_score
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 最佳方向已保存到: {output_file}")
        else:
            print("❌ 未找到有效的植入方向")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = analyze_direction()
    
    if success:
        print("\n🎉 方向分析完成！")
    else:
        print("\n❌ 方向分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
